// Expenses page JavaScript

// DOM Elements
const addExpenseBtn = document.getElementById('addExpenseBtn');
const expenseModal = document.getElementById('expenseModal');
const closeModal = document.getElementById('closeModal');
const cancelModal = document.getElementById('cancelModal');
const expenseForm = document.getElementById('expenseForm');
const expenseIdInput = document.getElementById('expenseId');
const expenseDescriptionInput = document.getElementById('expenseDescription');
const expenseCategorySelect = document.getElementById('expenseCategory');
const expenseSubcategorySelect = document.getElementById('expenseSubcategory');
const expenseAmountInput = document.getElementById('expenseAmount');
const expenseDateInput = document.getElementById('expenseDate');
const modalTitle = document.getElementById('modalTitle');
const filterCategorySelect = document.getElementById('filterCategory');
const filterStartDateInput = document.getElementById('filterStartDate');
const filterEndDateInput = document.getElementById('filterEndDate');
const filterButton = document.getElementById('filterButton');

// Quick filter buttons
const todayFilterBtn = document.getElementById('todayFilter');
const thisWeekFilterBtn = document.getElementById('thisWeekFilter');
const thisMonthFilterBtn = document.getElementById('thisMonthFilter');
const allExpensesFilterBtn = document.getElementById('allExpensesFilter');
const expensesTableBody = document.getElementById('expensesTableBody');

// State
let currentExpenseId = null;

// Event Listeners
if (addExpenseBtn) {
    addExpenseBtn.addEventListener('click', () => openExpenseModal());
}

if (closeModal) {
    closeModal.addEventListener('click', () => closeExpenseModal());
}

if (cancelModal) {
    cancelModal.addEventListener('click', () => closeExpenseModal());
}

if (expenseForm) {
    expenseForm.addEventListener('submit', handleExpenseFormSubmit);
}

if (expenseCategorySelect) {
    expenseCategorySelect.addEventListener('change', handleCategoryChange);
}

if (filterButton) {
    filterButton.addEventListener('click', handleFilterExpenses);
}

// Quick filter buttons
if (todayFilterBtn) {
    todayFilterBtn.addEventListener('click', () => {
        applyQuickFilter('today');
    });
}

if (thisWeekFilterBtn) {
    thisWeekFilterBtn.addEventListener('click', () => {
        applyQuickFilter('this_week');
    });
}

if (thisMonthFilterBtn) {
    thisMonthFilterBtn.addEventListener('click', () => {
        applyQuickFilter('this_month');
    });
}

if (allExpensesFilterBtn) {
    allExpensesFilterBtn.addEventListener('click', () => {
        applyQuickFilter('all');
    });
}

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    // Set today's date as default
    const today = new Date().toISOString().split('T')[0];
    if (expenseDateInput) {
        expenseDateInput.value = today;
    }
    
    loadCategories();
    loadExpenses();
});

// Load categories for dropdown
async function loadCategories() {
    try {
        const categories = await api.getCategories();
        renderCategoryOptions(categories);
        renderFilterCategoryOptions(categories);
    } catch (error) {
        console.error('Failed to load categories:', error);
        showNotification('Erreur lors du chargement des catégories', 'error');
    }
}

// Render category options for expense form
function renderCategoryOptions(categories) {
    if (!expenseCategorySelect) return;
    
    expenseCategorySelect.innerHTML = `
        <option value="">Sélectionner une catégorie</option>
        ${categories.map(category => `
            <option value="${category.id}">${category.name}</option>
        `).join('')}
    `;
}

// Render category options for filter
function renderFilterCategoryOptions(categories) {
    if (!filterCategorySelect) return;
    
    filterCategorySelect.innerHTML = `
        <option value="">Toutes les catégories</option>
        ${categories.map(category => `
            <option value="${category.id}">${category.name}</option>
        `).join('')}
    `;
}

// Handle category change
async function handleCategoryChange() {
    const categoryId = expenseCategorySelect.value;
    
    if (!categoryId) {
        if (expenseSubcategorySelect) {
            expenseSubcategorySelect.innerHTML = '<option value="">Sélectionner une sous-catégorie</option>';
        }
        return;
    }
    
    try {
        const subcategories = await api.getSubcategoriesByCategory(categoryId);
        renderSubcategoryOptions(subcategories);
    } catch (error) {
        console.error('Failed to load subcategories:', error);
        showNotification('Erreur lors du chargement des sous-catégories', 'error');
    }
}

// Render subcategory options
function renderSubcategoryOptions(subcategories) {
    if (!expenseSubcategorySelect) return;
    
    expenseSubcategorySelect.innerHTML = `
        <option value="">Sélectionner une sous-catégorie</option>
        ${subcategories.map(subcategory => `
            <option value="${subcategory.id}">${subcategory.name}</option>
        `).join('')}
    `;
}

// Load expenses from API
async function loadExpenses(filters = {}) {
    try {
        const expenses = await api.getExpenses(filters);
        renderExpenses(expenses);
    } catch (error) {
        console.error('Failed to load expenses:', error);
        showNotification('Erreur lors du chargement des dépenses', 'error');
    }
}

// Render expenses
function renderExpenses(expenses) {
    if (!expensesTableBody) return;
    
    if (expenses.length === 0) {
        expensesTableBody.innerHTML = `
            <tr>
                <td colspan="6" class="px-6 py-4 text-center text-gray-400">
                    Aucune dépense trouvée
                </td>
            </tr>
        `;
        return;
    }
    
    expensesTableBody.innerHTML = expenses.map(expense => `
        <tr data-id="${expense.id}">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium">${expense.description || 'Sans description'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-300">${expense.category_name || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-300">${expense.subcategory_name || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                ${formatDate(expense.date)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <span class="text-red-400">-${formatCurrency(expense.amount)}</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
                <div class="flex space-x-2">
                    <button class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg btn-icon edit-expense" data-id="${expense.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded-lg btn-icon delete-expense" data-id="${expense.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to edit buttons
    document.querySelectorAll('.edit-expense').forEach(button => {
        button.addEventListener('click', (e) => {
            const expenseId = e.currentTarget.getAttribute('data-id');
            editExpense(expenseId);
        });
    });
    
    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-expense').forEach(button => {
        button.addEventListener('click', (e) => {
            const expenseId = e.currentTarget.getAttribute('data-id');
            deleteExpense(expenseId);
        });
    });
}

// Open expense modal
async function openExpenseModal(expense = null) {
    currentExpenseId = expense ? expense.id : null;
    
    if (expense) {
        modalTitle.textContent = 'Modifier Dépense';
        expenseDescriptionInput.value = expense.description || '';
        expenseCategorySelect.value = expense.category_id || '';
        expenseAmountInput.value = expense.amount || '';
        expenseDateInput.value = expense.date || '';
        
        // Load subcategories for the selected category
        if (expense.category_id) {
            try {
                const subcategories = await api.getSubcategoriesByCategory(expense.category_id);
                renderSubcategoryOptions(subcategories);
                expenseSubcategorySelect.value = expense.subcategory_id || '';
            } catch (error) {
                console.error('Failed to load subcategories:', error);
            }
        }
    } else {
        modalTitle.textContent = 'Nouvelle Dépense';
        expenseForm.reset();
        
        // Set today's date as default
        const today = new Date().toISOString().split('T')[0];
        expenseDateInput.value = today;
    }
    
    expenseModal.classList.remove('hidden');
}

// Close expense modal
function closeExpenseModal() {
    if (expenseModal) {
        expenseModal.classList.add('hidden');
    }
    currentExpenseId = null;
    if (expenseForm) {
        expenseForm.reset();
    }
}

// Handle expense form submission
async function handleExpenseFormSubmit(e) {
    e.preventDefault();
    
    const description = expenseDescriptionInput.value.trim();
    const categoryId = expenseCategorySelect.value;
    const subcategoryId = expenseSubcategorySelect.value;
    const amount = expenseAmountInput.value;
    const date = expenseDateInput.value;
    
    if (!description || !categoryId || !subcategoryId || !amount || !date) {
        showNotification('Veuillez remplir tous les champs requis', 'error');
        return;
    }
    
    if (isNaN(amount) || parseFloat(amount) <= 0) {
        showNotification('Le montant doit être un nombre positif', 'error');
        return;
    }
    
    try {
        const expenseData = {
            description,
            category_id: categoryId,
            subcategory_id: subcategoryId,
            amount: parseFloat(amount),
            date
        };
        
        let result;
        if (currentExpenseId) {
            // Update existing expense
            result = await api.updateExpense(currentExpenseId, expenseData);
            showNotification('Dépense mise à jour avec succès', 'success');
        } else {
            // Create new expense
            result = await api.createExpense(expenseData);
            showNotification('Dépense créée avec succès', 'success');
        }
        
        closeExpenseModal();
        loadExpenses();
    } catch (error) {
        console.error('Failed to save expense:', error);
        showNotification('Erreur lors de l\'enregistrement de la dépense', 'error');
    }
}

// Edit expense
async function editExpense(expenseId) {
    try {
        const expense = await api.getExpense(expenseId);
        openExpenseModal(expense);
    } catch (error) {
        console.error('Failed to load expense:', error);
        showNotification('Erreur lors du chargement de la dépense', 'error');
    }
}

// Delete expense
async function deleteExpense(expenseId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette dépense ?')) {
        return;
    }
    
    try {
        await api.deleteExpense(expenseId);
        showNotification('Dépense supprimée avec succès', 'success');
        loadExpenses();
    } catch (error) {
        console.error('Failed to delete expense:', error);
        showNotification('Erreur lors de la suppression de la dépense', 'error');
    }
}

// Apply quick filter
function applyQuickFilter(period) {
    let filters = {};

    if (period !== 'all') {
        const dateRange = getDateRangeForPeriod(period);
        filters = { ...dateRange };

        // Update the date inputs to reflect the filter
        if (filterStartDateInput) filterStartDateInput.value = dateRange.start_date || '';
        if (filterEndDateInput) filterEndDateInput.value = dateRange.end_date || '';
    } else {
        // Clear date inputs for "all" filter
        if (filterStartDateInput) filterStartDateInput.value = '';
        if (filterEndDateInput) filterEndDateInput.value = '';
    }

    // Clear category filter
    if (filterCategorySelect) filterCategorySelect.value = '';

    // Update button styles
    updateQuickFilterButtonStyles(period);

    // Load expenses with filters
    loadExpenses(filters);

    showNotification(`Filtre appliqué: ${getPeriodLabel(period)}`, 'success');
}

// Update quick filter button styles
function updateQuickFilterButtonStyles(activePeriod) {
    const buttons = [
        { element: todayFilterBtn, period: 'today' },
        { element: thisWeekFilterBtn, period: 'this_week' },
        { element: thisMonthFilterBtn, period: 'this_month' },
        { element: allExpensesFilterBtn, period: 'all' }
    ];

    buttons.forEach(({ element, period }) => {
        if (element) {
            if (period === activePeriod) {
                element.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg transition';
            } else {
                element.className = 'px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition';
            }
        }
    });
}

// Handle filter expenses
function handleFilterExpenses() {
    const filters = {};

    if (filterCategorySelect && filterCategorySelect.value) {
        filters.category_id = filterCategorySelect.value;
    }

    if (filterStartDateInput && filterStartDateInput.value) {
        filters.start_date = filterStartDateInput.value;
    }

    if (filterEndDateInput && filterEndDateInput.value) {
        filters.end_date = filterEndDateInput.value;
    }

    // Reset quick filter button styles when using custom filters
    updateQuickFilterButtonStyles('custom');

    loadExpenses(filters);
}