// Date utility functions for the Expenses Manager application

/**
 * Get date range for predefined periods
 * @param {string} period - The period type (today, this_week, this_month, etc.)
 * @returns {object} Object with start_date and end_date
 */
function getDateRangeForPeriod(period) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch(period) {
        case 'today':
            return {
                start_date: formatDateForAPI(today),
                end_date: formatDateForAPI(today)
            };
        
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return {
                start_date: formatDateForAPI(yesterday),
                end_date: formatDateForAPI(yesterday)
            };
        
        case 'this_week':
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            return {
                start_date: formatDateForAPI(startOfWeek),
                end_date: formatDateForAPI(today)
            };
        
        case 'last_week':
            const lastWeekEnd = new Date(today);
            lastWeekEnd.setDate(today.getDate() - today.getDay() - 1);
            const lastWeekStart = new Date(lastWeekEnd);
            lastWeekStart.setDate(lastWeekEnd.getDate() - 6);
            return {
                start_date: formatDateForAPI(lastWeekStart),
                end_date: formatDateForAPI(lastWeekEnd)
            };
        
        case 'this_month':
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            return {
                start_date: formatDateForAPI(startOfMonth),
                end_date: formatDateForAPI(today)
            };
        
        case 'last_month':
            const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
            return {
                start_date: formatDateForAPI(lastMonthStart),
                end_date: formatDateForAPI(lastMonthEnd)
            };
        
        case 'this_quarter':
            const currentQuarter = Math.floor(now.getMonth() / 3);
            const startOfQuarter = new Date(now.getFullYear(), currentQuarter * 3, 1);
            return {
                start_date: formatDateForAPI(startOfQuarter),
                end_date: formatDateForAPI(today)
            };
        
        case 'this_year':
            const startOfYear = new Date(now.getFullYear(), 0, 1);
            return {
                start_date: formatDateForAPI(startOfYear),
                end_date: formatDateForAPI(today)
            };
        
        case 'last_30_days':
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            return {
                start_date: formatDateForAPI(thirtyDaysAgo),
                end_date: formatDateForAPI(today)
            };
        
        case 'last_90_days':
            const ninetyDaysAgo = new Date(today);
            ninetyDaysAgo.setDate(today.getDate() - 90);
            return {
                start_date: formatDateForAPI(ninetyDaysAgo),
                end_date: formatDateForAPI(today)
            };
        
        default:
            return {};
    }
}

/**
 * Format date for API (YYYY-MM-DD)
 * @param {Date} date - The date to format
 * @returns {string} Formatted date string
 */
function formatDateForAPI(date) {
    return date.toISOString().split('T')[0];
}

/**
 * Format date for display (localized)
 * @param {string|Date} date - The date to format
 * @param {object} options - Formatting options
 * @returns {string} Formatted date string
 */
function formatDateForDisplay(date, options = {}) {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const defaultOptions = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    };
    return dateObj.toLocaleDateString('fr-FR', { ...defaultOptions, ...options });
}

/**
 * Get period label for display
 * @param {string} period - The period type
 * @returns {string} Human-readable period label
 */
function getPeriodLabel(period) {
    const labels = {
        'today': "Aujourd'hui",
        'yesterday': 'Hier',
        'this_week': 'Cette semaine',
        'last_week': 'Semaine dernière',
        'this_month': 'Ce mois',
        'last_month': 'Mois dernier',
        'this_quarter': 'Ce trimestre',
        'this_year': 'Cette année',
        'last_30_days': '30 derniers jours',
        'last_90_days': '90 derniers jours',
        'all': 'Toute la période'
    };
    return labels[period] || period;
}

/**
 * Get chart labels for time period
 * @param {string} period - The period type (daily, weekly, monthly)
 * @param {object} dateRange - Start and end dates
 * @returns {array} Array of labels for chart
 */
function getChartLabelsForPeriod(period, dateRange) {
    const labels = [];
    const start = new Date(dateRange.start_date);
    const end = new Date(dateRange.end_date);
    
    switch(period) {
        case 'daily':
            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                labels.push(formatDateForDisplay(new Date(d), { month: 'short', day: 'numeric' }));
            }
            break;
        
        case 'weekly':
            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 7)) {
                const weekEnd = new Date(d);
                weekEnd.setDate(d.getDate() + 6);
                labels.push(`${formatDateForDisplay(new Date(d), { month: 'short', day: 'numeric' })} - ${formatDateForDisplay(weekEnd, { month: 'short', day: 'numeric' })}`);
            }
            break;
        
        case 'monthly':
        default:
            for (let d = new Date(start); d <= end; d.setMonth(d.getMonth() + 1)) {
                labels.push(formatDateForDisplay(new Date(d), { year: 'numeric', month: 'short' }));
            }
            break;
    }
    
    return labels;
}

/**
 * Validate date range
 * @param {string} startDate - Start date
 * @param {string} endDate - End date
 * @returns {object} Validation result with isValid and message
 */
function validateDateRange(startDate, endDate) {
    if (!startDate || !endDate) {
        return { isValid: false, message: 'Les dates de début et de fin sont requises' };
    }
    
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return { isValid: false, message: 'Format de date invalide' };
    }
    
    if (start > end) {
        return { isValid: false, message: 'La date de début doit être antérieure à la date de fin' };
    }
    
    const today = new Date();
    if (start > today) {
        return { isValid: false, message: 'La date de début ne peut pas être dans le futur' };
    }
    
    return { isValid: true, message: 'Plage de dates valide' };
}

/**
 * Get relative time string
 * @param {string|Date} date - The date
 * @returns {string} Relative time string
 */
function getRelativeTime(date) {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diffInSeconds = Math.floor((now - dateObj) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Il y a quelques secondes';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `Il y a ${minutes} minute${minutes > 1 ? 's' : ''}`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `Il y a ${hours} heure${hours > 1 ? 's' : ''}`;
    } else if (diffInSeconds < 2592000) {
        const days = Math.floor(diffInSeconds / 86400);
        return `Il y a ${days} jour${days > 1 ? 's' : ''}`;
    } else {
        return formatDateForDisplay(dateObj);
    }
}

// Export functions for use in other modules
window.getDateRangeForPeriod = getDateRangeForPeriod;
window.formatDateForAPI = formatDateForAPI;
window.formatDateForDisplay = formatDateForDisplay;
window.getPeriodLabel = getPeriodLabel;
window.getChartLabelsForPeriod = getChartLabelsForPeriod;
window.validateDateRange = validateDateRange;
window.getRelativeTime = getRelativeTime;

console.log('Date utilities loaded');
