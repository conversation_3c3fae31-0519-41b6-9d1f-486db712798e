// Dashboard page JavaScript

// DOM Elements
const totalExpensesElement = document.getElementById('totalExpenses');
const categoryCountElement = document.getElementById('categoryCount');
const transactionCountElement = document.getElementById('transactionCount');
const addExpenseBtn = document.getElementById('addExpenseBtn');
const monthViewBtn = document.getElementById('monthView');
const quarterViewBtn = document.getElementById('quarterView');
const yearViewBtn = document.getElementById('yearView');
const dashboardChartCtx = document.getElementById('dashboardChart');
const recentExpensesTableBody = document.getElementById('recentExpensesTableBody');

// Period filter buttons
const todayFilterBtn = document.getElementById('todayFilter');
const thisWeekFilterBtn = document.getElementById('thisWeekFilter');
const thisMonthFilterBtn = document.getElementById('thisMonthFilter');
const thisQuarterFilterBtn = document.getElementById('thisQuarterFilter');
const thisYearFilterBtn = document.getElementById('thisYearFilter');
const customPeriodBtn = document.getElementById('customPeriodBtn');
const customPeriodModal = document.getElementById('customPeriodModal');
const closeCustomPeriodModal = document.getElementById('closeCustomPeriodModal');
const cancelCustomPeriod = document.getElementById('cancelCustomPeriod');
const customStartDate = document.getElementById('customStartDate');
const customEndDate = document.getElementById('customEndDate');
const applyCustomPeriodBtn = document.getElementById('applyCustomPeriod');

// Chart instance
let dashboardChart = null;

// Current filters
let currentFilters = { period: 'today' };

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadDashboardData();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    if (addExpenseBtn) {
        addExpenseBtn.addEventListener('click', () => {
            window.location.href = '/expenses';
        });
    }

    // Chart period buttons
    if (monthViewBtn) {
        monthViewBtn.addEventListener('click', () => {
            updateChartPeriod('monthly');
        });
    }

    if (quarterViewBtn) {
        quarterViewBtn.addEventListener('click', () => {
            updateChartPeriod('monthly');
        });
    }

    if (yearViewBtn) {
        yearViewBtn.addEventListener('click', () => {
            updateChartPeriod('monthly');
        });
    }

    // Period filter buttons
    if (todayFilterBtn) {
        todayFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('today');
        });
    }

    if (thisWeekFilterBtn) {
        thisWeekFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_week');
        });
    }

    if (thisMonthFilterBtn) {
        thisMonthFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_month');
        });
    }

    if (thisQuarterFilterBtn) {
        thisQuarterFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_quarter');
        });
    }

    if (thisYearFilterBtn) {
        thisYearFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_year');
        });
    }

    if (customPeriodBtn) {
        customPeriodBtn.addEventListener('click', () => {
            openCustomPeriodModal();
        });
    }

    if (closeCustomPeriodModal) {
        closeCustomPeriodModal.addEventListener('click', () => {
            closeCustomPeriodModalHandler();
        });
    }

    if (cancelCustomPeriod) {
        cancelCustomPeriod.addEventListener('click', () => {
            closeCustomPeriodModalHandler();
        });
    }

    if (applyCustomPeriodBtn) {
        applyCustomPeriodBtn.addEventListener('click', () => {
            applyCustomPeriod();
        });
    }

    // Close modal when clicking outside
    if (customPeriodModal) {
        customPeriodModal.addEventListener('click', (e) => {
            if (e.target === customPeriodModal) {
                closeCustomPeriodModalHandler();
            }
        });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && customPeriodModal && !customPeriodModal.classList.contains('hidden')) {
            closeCustomPeriodModalHandler();
        }
    });
}

// Load dashboard data
async function loadDashboardData() {
    try {
        // Prepare filters based on current period
        const filters = {};
        if (currentFilters.period) {
            filters.period = currentFilters.period;
        }
        if (currentFilters.start_date && currentFilters.end_date) {
            filters.start_date = currentFilters.start_date;
            filters.end_date = currentFilters.end_date;
        }

        // Load summary data with filters
        const summary = await api.getExpenseSummary(filters);
        updateSummaryCards(summary);

        // Load recent expenses (always show recent, not filtered)
        const expenses = await api.getExpenses({ limit: 5 });
        renderRecentExpenses(expenses);

        // Load chart data with filters
        await loadChartData();
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        showNotification('Erreur lors du chargement des données du tableau de bord', 'error');
    }
}

// Update summary cards
function updateSummaryCards(summary) {
    if (!summary || summary.length === 0) {
        if (totalExpensesElement) totalExpensesElement.textContent = '€0.00';
        if (categoryCountElement) categoryCountElement.textContent = '0';
        if (transactionCountElement) transactionCountElement.textContent = '0';
        return;
    }
    
    // Calculate totals
    let totalExpenses = 0;
    let totalTransactions = 0;
    
    summary.forEach(category => {
        totalExpenses += parseFloat(category.total_amount) || 0;
        totalTransactions += parseInt(category.expense_count) || 0;
    });
    
    // Update DOM elements
    if (totalExpensesElement) {
        totalExpensesElement.textContent = formatCurrency(totalExpenses);
    }
    
    if (categoryCountElement) {
        categoryCountElement.textContent = summary.length;
    }
    
    if (transactionCountElement) {
        transactionCountElement.textContent = totalTransactions;
    }
}

// Render recent expenses
function renderRecentExpenses(expenses) {
    if (!recentExpensesTableBody) return;
    
    if (!expenses || expenses.length === 0) {
        recentExpensesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-400">
                    Aucune dépense récente
                </td>
            </tr>
        `;
        return;
    }
    
    recentExpensesTableBody.innerHTML = expenses.map(expense => `
        <tr>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium">${expense.description || 'Sans description'}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-300">${expense.category_name || 'N/A'}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                ${formatDate(expense.date)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <span class="text-red-400">-${formatCurrency(expense.amount)}</span>
            </td>
        </tr>
    `).join('');
}

// Load chart data with real API data
async function loadChartData() {
    if (!dashboardChartCtx) return;

    try {
        // Prepare filters for chart data
        const filters = {};
        if (currentFilters.period) {
            filters.time_period = currentFilters.period;
        }
        if (currentFilters.start_date && currentFilters.end_date) {
            filters.start_date = currentFilters.start_date;
            filters.end_date = currentFilters.end_date;
        }

        // Get time period data from API
        const timeData = await api.getExpensesByTimePeriod('monthly', filters);

        // Process data for chart
        const labels = timeData.map(item => {
            // Convert period (YYYY-MM) to readable format
            const [year, month] = item.period.split('-');
            const date = new Date(year, month - 1);
            return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'short' });
        });

        const amounts = timeData.map(item => parseFloat(item.total_amount) || 0);

        // Destroy existing chart if it exists
        if (dashboardChart) {
            dashboardChart.destroy();
        }

        const data = {
            labels: labels,
            datasets: [{
                label: 'Dépenses',
                data: amounts,
                borderColor: 'rgb(99, 102, 241)',
                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                tension: 0.4,
                fill: true
            }]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9ca3af',
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        },
                        ticks: {
                            color: '#9ca3af'
                        }
                    }
                }
            }
        };

        dashboardChart = new Chart(dashboardChartCtx, config);
    } catch (error) {
        console.error('Failed to load chart data:', error);
        // Fallback to empty chart
        initializeEmptyChart();
    }
}

// Initialize empty chart when no data is available
function initializeEmptyChart() {
    if (!dashboardChartCtx) return;

    if (dashboardChart) {
        dashboardChart.destroy();
    }

    const data = {
        labels: ['Aucune donnée'],
        datasets: [{
            label: 'Dépenses',
            data: [0],
            borderColor: 'rgb(99, 102, 241)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };

    const config = {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9ca3af'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9ca3af'
                    }
                }
            }
        }
    };

    dashboardChart = new Chart(dashboardChartCtx, config);
}

// Apply period filter
function applyPeriodFilter(period) {
    // Update current filters
    currentFilters = { period: period };

    // Update button styles
    updatePeriodButtonStyles(period);

    // Close custom period modal if open
    if (customPeriodModal && !customPeriodModal.classList.contains('hidden')) {
        closeCustomPeriodModalHandler();
    }

    // Reload data with new filters
    loadDashboardData();

    showNotification(`Filtre appliqué: ${getPeriodLabel(period)}`, 'success');
}

// Open custom period modal
function openCustomPeriodModal() {
    if (customPeriodModal) {
        // Set default dates
        const today = new Date();
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

        if (customStartDate) customStartDate.value = formatDateForAPI(lastMonth);
        if (customEndDate) customEndDate.value = formatDateForAPI(today);

        customPeriodModal.classList.remove('hidden');
    }
}

// Close custom period modal
function closeCustomPeriodModalHandler() {
    if (customPeriodModal) {
        customPeriodModal.classList.add('hidden');
    }
}

// Apply custom period
function applyCustomPeriod() {
    const startDate = customStartDate?.value;
    const endDate = customEndDate?.value;

    if (!startDate || !endDate) {
        showNotification('Veuillez sélectionner les dates de début et de fin', 'error');
        return;
    }

    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
        showNotification(validation.message, 'error');
        return;
    }

    // Update current filters
    currentFilters = {
        start_date: startDate,
        end_date: endDate
    };

    // Update button styles
    updatePeriodButtonStyles('custom');

    // Close modal
    closeCustomPeriodModalHandler();

    // Reload data with new filters
    loadDashboardData();

    showNotification(`Période personnalisée appliquée: ${formatDateForDisplay(startDate)} - ${formatDateForDisplay(endDate)}`, 'success');
}

// Update period button styles
function updatePeriodButtonStyles(activePeriod) {
    const buttons = [
        { element: todayFilterBtn, period: 'today' },
        { element: thisWeekFilterBtn, period: 'this_week' },
        { element: thisMonthFilterBtn, period: 'this_month' },
        { element: thisQuarterFilterBtn, period: 'this_quarter' },
        { element: thisYearFilterBtn, period: 'this_year' },
        { element: customPeriodBtn, period: 'custom' }
    ];

    buttons.forEach(({ element, period }) => {
        if (element) {
            if (period === activePeriod) {
                element.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg transition';
            } else {
                element.className = 'px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition';
            }
        }
    });
}

// Update chart period (for chart view buttons)
function updateChartPeriod(period) {
    // Update button styles
    if (monthViewBtn) monthViewBtn.className = 'px-3 py-1 text-sm bg-gray-700 rounded-lg';
    if (quarterViewBtn) quarterViewBtn.className = 'px-3 py-1 text-sm bg-gray-700 rounded-lg';
    if (yearViewBtn) yearViewBtn.className = 'px-3 py-1 text-sm bg-gray-700 rounded-lg';

    // Highlight selected period
    switch(period) {
        case 'monthly':
            if (monthViewBtn) monthViewBtn.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg';
            break;
        case 'quarterly':
            if (quarterViewBtn) quarterViewBtn.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg';
            break;
        case 'yearly':
            if (yearViewBtn) yearViewBtn.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg';
            break;
    }

    // Reload chart with new period
    loadChartData();
}