// Reports page JavaScript

// DOM Elements
const exportReportBtn = document.getElementById('exportReport');
const totalExpensesElement = document.getElementById('totalExpenses');
const categoryCountElement = document.getElementById('categoryCount');
const transactionCountElement = document.getElementById('transactionCount');
const averageMonthlyElement = document.getElementById('averageMonthly');
const categoriesTableBody = document.getElementById('categoriesTableBody');
const categoryChartCtx = document.getElementById('categoryChart');
const timeChartCtx = document.getElementById('timeChart');

// Period filter buttons
const todayFilterBtn = document.getElementById('todayFilter');
const thisWeekFilterBtn = document.getElementById('thisWeekFilter');
const thisMonthFilterBtn = document.getElementById('thisMonthFilter');
const thisQuarterFilterBtn = document.getElementById('thisQuarterFilter');
const thisYearFilterBtn = document.getElementById('thisYearFilter');
const allTimeFilterBtn = document.getElementById('allTimeFilter');
const customPeriodBtn = document.getElementById('customPeriodBtn');
const customPeriodSelector = document.getElementById('customPeriodSelector');
const customStartDate = document.getElementById('customStartDate');
const customEndDate = document.getElementById('customEndDate');
const applyCustomPeriodBtn = document.getElementById('applyCustomPeriod');

// Chart instances
let categoryChart = null;
let timeChart = null;

// Current filters
let currentFilters = { period: 'this_month' };

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadReportData();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', exportReport);
    }

    // Period filter buttons
    if (todayFilterBtn) {
        todayFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('today');
        });
    }

    if (thisWeekFilterBtn) {
        thisWeekFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_week');
        });
    }

    if (thisMonthFilterBtn) {
        thisMonthFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_month');
        });
    }

    if (thisQuarterFilterBtn) {
        thisQuarterFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_quarter');
        });
    }

    if (thisYearFilterBtn) {
        thisYearFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('this_year');
        });
    }

    if (allTimeFilterBtn) {
        allTimeFilterBtn.addEventListener('click', () => {
            applyPeriodFilter('all');
        });
    }

    if (customPeriodBtn) {
        customPeriodBtn.addEventListener('click', () => {
            toggleCustomPeriodSelector();
        });
    }

    if (applyCustomPeriodBtn) {
        applyCustomPeriodBtn.addEventListener('click', () => {
            applyCustomPeriod();
        });
    }
}

// Load report data
async function loadReportData() {
    try {
        // Prepare filters based on current period
        const filters = {};
        if (currentFilters.period && currentFilters.period !== 'all') {
            filters.period = currentFilters.period;
        }
        if (currentFilters.start_date && currentFilters.end_date) {
            filters.start_date = currentFilters.start_date;
            filters.end_date = currentFilters.end_date;
        }

        const summary = await api.getExpenseSummary(filters);
        console.log('Received summary data:', summary); // Log for debugging
        updateSummaryCards(summary);
        renderCategoriesTable(summary);
        await initializeCharts(summary, filters);
    } catch (error) {
        console.error('Failed to load report data:', error);
        showNotification('Erreur lors du chargement des données de rapport', 'error');
    }
}

// Update summary cards
function updateSummaryCards(summary) {
    if (!summary || summary.length === 0) {
        if (totalExpensesElement) totalExpensesElement.textContent = '€0.00';
        if (categoryCountElement) categoryCountElement.textContent = '0';
        if (transactionCountElement) transactionCountElement.textContent = '0';
        if (averageMonthlyElement) averageMonthlyElement.textContent = '€0.00';
        return;
    }
    
    // Calculate totals
    let totalExpenses = 0;
    let totalTransactions = 0;
    
    summary.forEach(category => {
        totalExpenses += parseFloat(category.total_amount) || 0;
        totalTransactions += parseInt(category.expense_count) || 0;
    });
    
    // Calculate average monthly (assuming data is for current year)
    const averageMonthly = totalExpenses / 12;
    
    // Update DOM elements
    if (totalExpensesElement) {
        totalExpensesElement.textContent = formatCurrency(totalExpenses);
    }
    
    if (categoryCountElement) {
        categoryCountElement.textContent = summary.length;
    }
    
    if (transactionCountElement) {
        transactionCountElement.textContent = totalTransactions;
    }
    
    if (averageMonthlyElement) {
        averageMonthlyElement.textContent = formatCurrency(averageMonthly);
    }
}

// Render categories table
function renderCategoriesTable(categories) {
    if (!categoriesTableBody) return;
    
    if (!categories || categories.length === 0) {
        categoriesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-400">
                    Aucune donnée disponible
                </td>
            </tr>
        `;
        return;
    }
    
    // Sort categories by total amount (descending)
    const sortedCategories = [...categories].sort((a, b) => 
        (parseFloat(b.total_amount) || 0) - (parseFloat(a.total_amount) || 0)
    );
    
    // Calculate total for percentage calculation
    const totalAmount = sortedCategories.reduce((sum, category) => 
        sum + (parseFloat(category.total_amount) || 0), 0
    );
    
    categoriesTableBody.innerHTML = sortedCategories.map((category, index) => {
        // Assign colors based on index
        const colors = ['indigo', 'purple', 'cyan', 'pink', 'red', 'yellow', 'green', 'blue'];
        const color = colors[index % colors.length];
        const icon = ['utensils', 'home', 'car', 'gamepad', 'heartbeat', 'tshirt', 'book', 'plane'][index % colors.length];
        
        const amount = parseFloat(category.total_amount) || 0;
        const count = parseInt(category.expense_count) || 0;
        const percentage = totalAmount > 0 ? (amount / totalAmount * 100).toFixed(1) : 0;
        
        return `
            <tr>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 bg-${color}-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-${icon}"></i>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium">${category.category_name}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                    ${count}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    ${formatCurrency(amount)}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="w-24 bg-gray-700 rounded-full h-2 mr-2">
                            <div class="bg-${color}-500 h-2 rounded-full" style="width: ${percentage}%"></div>
                        </div>
                        <span class="text-sm text-gray-300">${percentage}%</span>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Initialize charts
async function initializeCharts(categories, filters = {}) {
    // Initialize category chart
    if (categoryChartCtx) {
        // Destroy existing chart if it exists
        if (categoryChart) {
            categoryChart.destroy();
        }

        if (categories && categories.length > 0) {
            // Prepare data for category chart
            const labels = categories.map(cat => cat.category_name);
            const data = categories.map(cat => parseFloat(cat.total_amount) || 0);
            const backgroundColors = [
                'rgba(99, 102, 241, 0.7)',
                'rgba(139, 92, 246, 0.7)',
                'rgba(6, 182, 212, 0.7)',
                'rgba(236, 72, 153, 0.7)',
                'rgba(239, 68, 68, 0.7)',
                'rgba(245, 158, 11, 0.7)',
                'rgba(16, 185, 129, 0.7)',
                'rgba(59, 130, 246, 0.7)'
            ];

            const categoryChartData = {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: backgroundColors.slice(0, labels.length),
                    borderWidth: 0
                }]
            };

            const categoryChartConfig = {
                type: 'doughnut',
                data: categoryChartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                            labels: {
                                color: '#f9fafb',
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                }
            };

            categoryChart = new Chart(categoryChartCtx, categoryChartConfig);
        } else {
            // Show empty state
            initializeEmptyCategoryChart();
        }
    }

    // Initialize time chart with real data
    if (timeChartCtx) {
        // Destroy existing chart if it exists
        if (timeChart) {
            timeChart.destroy();
        }

        try {
            // Get time period data from API
            const timeFilters = { ...filters };
            if (filters.period) {
                timeFilters.time_period = filters.period;
            }

            const timeData = await api.getExpensesByTimePeriod('monthly', timeFilters);

            if (timeData && timeData.length > 0) {
                // Process data for chart
                const timeLabels = timeData.map(item => {
                    // Convert period (YYYY-MM) to readable format
                    const [year, month] = item.period.split('-');
                    const date = new Date(year, month - 1);
                    return date.toLocaleDateString('fr-FR', { year: 'numeric', month: 'short' });
                });

                const amounts = timeData.map(item => parseFloat(item.total_amount) || 0);

                const timeChartData = {
                    labels: timeLabels,
                    datasets: [{
                        label: 'Dépenses mensuelles',
                        data: amounts,
                        borderColor: 'rgb(99, 102, 241)',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                };

                const timeChartConfig = {
                    type: 'line',
                    data: timeChartData,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#9ca3af',
                                    callback: function(value) {
                                        return formatCurrency(value);
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(255, 255, 255, 0.1)'
                                },
                                ticks: {
                                    color: '#9ca3af'
                                }
                            }
                        }
                    }
                };

                timeChart = new Chart(timeChartCtx, timeChartConfig);
            } else {
                // Show empty state
                initializeEmptyTimeChart();
            }
        } catch (error) {
            console.error('Failed to load time chart data:', error);
            initializeEmptyTimeChart();
        }
    }
}

// Initialize empty category chart
function initializeEmptyCategoryChart() {
    if (!categoryChartCtx) return;

    const categoryChartData = {
        labels: ['Aucune donnée'],
        datasets: [{
            data: [1],
            backgroundColor: ['rgba(156, 163, 175, 0.5)'],
            borderWidth: 0
        }]
    };

    const categoryChartConfig = {
        type: 'doughnut',
        data: categoryChartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        color: '#f9fafb',
                        font: {
                            size: 12
                        }
                    }
                }
            }
        }
    };

    categoryChart = new Chart(categoryChartCtx, categoryChartConfig);
}

// Initialize empty time chart
function initializeEmptyTimeChart() {
    if (!timeChartCtx) return;

    const timeChartData = {
        labels: ['Aucune donnée'],
        datasets: [{
            label: 'Dépenses',
            data: [0],
            borderColor: 'rgb(156, 163, 175)',
            backgroundColor: 'rgba(156, 163, 175, 0.1)',
            tension: 0.4,
            fill: true
        }]
    };

    const timeChartConfig = {
        type: 'line',
        data: timeChartData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9ca3af'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(255, 255, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9ca3af'
                    }
                }
            }
        }
    };

    timeChart = new Chart(timeChartCtx, timeChartConfig);
}

// Apply period filter
function applyPeriodFilter(period) {
    // Update current filters
    currentFilters = { period: period };

    // Update button styles
    updatePeriodButtonStyles(period);

    // Hide custom period selector
    if (customPeriodSelector) {
        customPeriodSelector.classList.add('hidden');
    }

    // Reload data with new filters
    loadReportData();

    showNotification(`Filtre appliqué: ${getPeriodLabel(period)}`, 'success');
}

// Toggle custom period selector
function toggleCustomPeriodSelector() {
    if (customPeriodSelector) {
        customPeriodSelector.classList.toggle('hidden');

        if (!customPeriodSelector.classList.contains('hidden')) {
            // Set default dates
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            if (customStartDate) customStartDate.value = formatDateForAPI(lastMonth);
            if (customEndDate) customEndDate.value = formatDateForAPI(today);
        }
    }
}

// Apply custom period
function applyCustomPeriod() {
    const startDate = customStartDate?.value;
    const endDate = customEndDate?.value;

    if (!startDate || !endDate) {
        showNotification('Veuillez sélectionner les dates de début et de fin', 'error');
        return;
    }

    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
        showNotification(validation.message, 'error');
        return;
    }

    // Update current filters
    currentFilters = {
        start_date: startDate,
        end_date: endDate
    };

    // Update button styles
    updatePeriodButtonStyles('custom');

    // Hide custom period selector
    customPeriodSelector.classList.add('hidden');

    // Reload data with new filters
    loadReportData();

    showNotification(`Période personnalisée appliquée: ${formatDateForDisplay(startDate)} - ${formatDateForDisplay(endDate)}`, 'success');
}

// Update period button styles
function updatePeriodButtonStyles(activePeriod) {
    const buttons = [
        { element: todayFilterBtn, period: 'today' },
        { element: thisWeekFilterBtn, period: 'this_week' },
        { element: thisMonthFilterBtn, period: 'this_month' },
        { element: thisQuarterFilterBtn, period: 'this_quarter' },
        { element: thisYearFilterBtn, period: 'this_year' },
        { element: allTimeFilterBtn, period: 'all' },
        { element: customPeriodBtn, period: 'custom' }
    ];

    buttons.forEach(({ element, period }) => {
        if (element) {
            if (period === activePeriod) {
                element.className = 'px-3 py-1 text-sm bg-indigo-600 rounded-lg transition';
            } else {
                element.className = 'px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition';
            }
        }
    });
}

// Export report
function exportReport() {
    showNotification('Fonction d\'exportation à implémenter', 'info');
    // In a real app, this would generate and download a report file
}