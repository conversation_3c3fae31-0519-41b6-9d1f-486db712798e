// Categories page JavaScript

// DOM Elements
const addCategoryBtn = document.getElementById('addCategoryBtn');
const categoryModal = document.getElementById('categoryModal');
const closeModal = document.getElementById('closeModal');
const cancelModal = document.getElementById('cancelModal');
const categoryForm = document.getElementById('categoryForm');
const categoryIdInput = document.getElementById('categoryId');
const categoryNameInput = document.getElementById('categoryName');
const categoryDescriptionInput = document.getElementById('categoryDescription');
const modalTitle = document.getElementById('modalTitle');
const categoriesTableBody = document.getElementById('categoriesTableBody');

// State
let currentCategoryId = null;

// Event Listeners
if (addCategoryBtn) {
    addCategoryBtn.addEventListener('click', () => openCategoryModal());
}

if (closeModal) {
    closeModal.addEventListener('click', () => closeCategoryModal());
}

if (cancelModal) {
    cancelModal.addEventListener('click', () => closeCategoryModal());
}

if (categoryForm) {
    categoryForm.addEventListener('submit', handleCategoryFormSubmit);
}

// Initialize page
document.addEventListener('DOMContentLoaded', () => {
    loadCategories();
});

// Load categories from API
async function loadCategories() {
    try {
        const categories = await api.getCategories();
        renderCategories(categories);
    } catch (error) {
        console.error('Failed to load categories:', error);
        showNotification('Erreur lors du chargement des catégories', 'error');
    }
}

// Render categories in table
function renderCategories(categories) {
    if (!categoriesTableBody) return;
    
    if (categories.length === 0) {
        categoriesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="px-6 py-4 text-center text-gray-400">
                    Aucune catégorie trouvée
                </td>
            </tr>
        `;
        return;
    }
    
    categoriesTableBody.innerHTML = categories.map(category => `
        <tr data-id="${category.id}">
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-indigo-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium">${category.name}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                ${category.description || 'Aucune description'}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                ${category.subcategory_count || 0}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm">
                <div class="flex space-x-2">
                    <button class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded-lg btn-icon edit-category" data-id="${category.id}">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-red-400 hover:bg-gray-700 rounded-lg btn-icon delete-category" data-id="${category.id}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    // Add event listeners to edit buttons
    document.querySelectorAll('.edit-category').forEach(button => {
        button.addEventListener('click', (e) => {
            const categoryId = e.currentTarget.getAttribute('data-id');
            editCategory(categoryId);
        });
    });
    
    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-category').forEach(button => {
        button.addEventListener('click', (e) => {
            const categoryId = e.currentTarget.getAttribute('data-id');
            deleteCategory(categoryId);
        });
    });
}

// Open category modal
function openCategoryModal(category = null) {
    currentCategoryId = category ? category.id : null;
    
    if (category) {
        modalTitle.textContent = 'Modifier Catégorie';
        categoryNameInput.value = category.name || '';
        categoryDescriptionInput.value = category.description || '';
    } else {
        modalTitle.textContent = 'Nouvelle Catégorie';
        categoryForm.reset();
    }
    
    categoryModal.classList.remove('hidden');
}

// Close category modal
function closeCategoryModal() {
    if (categoryModal) {
        categoryModal.classList.add('hidden');
    }
    currentCategoryId = null;
    if (categoryForm) {
        categoryForm.reset();
    }
}

// Handle category form submission
async function handleCategoryFormSubmit(e) {
    e.preventDefault();
    
    const name = categoryNameInput.value.trim();
    const description = categoryDescriptionInput.value.trim();
    
    if (!name) {
        showNotification('Veuillez entrer un nom pour la catégorie', 'error');
        return;
    }
    
    try {
        const categoryData = { name, description };
        
        if (currentCategoryId) {
            // Update existing category
            await api.updateCategory(currentCategoryId, categoryData);
            showNotification('Catégorie mise à jour avec succès', 'success');
        } else {
            // Create new category
            await api.createCategory(categoryData);
            showNotification('Catégorie créée avec succès', 'success');
        }
        
        closeCategoryModal();
        loadCategories();
    } catch (error) {
        console.error('Failed to save category:', error);
        showNotification('Erreur lors de l\'enregistrement de la catégorie', 'error');
    }
}

// Edit category
async function editCategory(categoryId) {
    try {
        const category = await api.getCategoryById(categoryId);
        openCategoryModal(category);
    } catch (error) {
        console.error('Failed to load category:', error);
        showNotification('Erreur lors du chargement de la catégorie', 'error');
    }
}

// Delete category
async function deleteCategory(categoryId) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette catégorie ? Toutes les sous-catégories et dépenses associées seront également supprimées.')) {
        return;
    }
    
    try {
        await api.deleteCategory(categoryId);
        showNotification('Catégorie supprimée avec succès', 'success');
        loadCategories();
    } catch (error) {
        console.error('Failed to delete category:', error);
        showNotification('Erreur lors de la suppression de la catégorie', 'error');
    }
}