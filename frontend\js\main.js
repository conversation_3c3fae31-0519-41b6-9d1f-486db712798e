// Main JavaScript file for the Expenses Manager application

// DOM Elements
const darkModeToggle = document.querySelector('.fa-moon')?.closest('button');
const mobileMenuButton = document.querySelector('.fa-bars')?.closest('button');
const mobileMenu = document.querySelector('.md\\:hidden');

// Dark Mode Toggle
if (darkModeToggle) {
    darkModeToggle.addEventListener('click', () => {
        // In a real app, this would toggle dark mode
        console.log('Dark mode toggle clicked');
        // For now, we'll just show a message
        showNotification('Le mode sombre sera implémenté dans une version future', 'info');
    });
}

// Mobile Menu Toggle
if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
    });
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 transform transition duration-300 ease-in-out`;
    
    // Set styles based on type
    switch(type) {
        case 'success':
            notification.classList.add('bg-green-500', 'text-white');
            break;
        case 'error':
            notification.classList.add('bg-red-500', 'text-white');
            break;
        case 'warning':
            notification.classList.add('bg-yellow-500', 'text-gray-900');
            break;
        case 'info':
        default:
            notification.classList.add('bg-blue-500', 'text-white');
    }
    
    notification.textContent = message;
    
    // Add to DOM
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.classList.add('opacity-0', 'translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('fr-FR', options);
}

// API Service
class ApiService {
    constructor() {
        this.baseUrl = 'http://localhost:3000/api';
    }
    
    async request(endpoint, options = {}) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API request failed:', error);
            showNotification('Erreur de connexion avec le serveur', 'error');
            throw error;
        }
    }
    
    // Categories
    async getCategories() {
        try {
            return await this.request('/categories');
        } catch (error) {
            console.error('Failed to get categories:', error);
            throw error;
        }
    }
    
    async getCategoryById(id) {
        try {
            return await this.request(`/categories/${id}`);
        } catch (error) {
            console.error('Failed to get category by ID:', error);
            throw error;
        }
    }
    
    async createCategory(category) {
        try {
            return await this.request('/categories', {
                method: 'POST',
                body: JSON.stringify(category)
            });
        } catch (error) {
            console.error('Failed to create category:', error);
            throw error;
        }
    }
    
    async updateCategory(id, category) {
        try {
            return await this.request(`/categories/${id}`, {
                method: 'PUT',
                body: JSON.stringify(category)
            });
        } catch (error) {
            console.error('Failed to update category:', error);
            throw error;
        }
    }
    
    async deleteCategory(id) {
        try {
            return await this.request(`/categories/${id}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Failed to delete category:', error);
            throw error;
        }
    }
    
    // Subcategories
    async getSubcategories() {
        try {
            return await this.request('/subcategories');
        } catch (error) {
            console.error('Failed to get subcategories:', error);
            throw error;
        }
    }
    
    async getSubcategoryById(id) {
        try {
            return await this.request(`/subcategories/${id}`);
        } catch (error) {
            console.error('Failed to get subcategory by ID:', error);
            throw error;
        }
    }
    
    async getSubcategoriesByCategory(categoryId) {
        try {
            return await this.request(`/subcategories/category/${categoryId}`);
        } catch (error) {
            console.error('Failed to get subcategories by category:', error);
            throw error;
        }
    }
    
    async createSubcategory(subcategory) {
        try {
            return await this.request('/subcategories', {
                method: 'POST',
                body: JSON.stringify(subcategory)
            });
        } catch (error) {
            console.error('Failed to create subcategory:', error);
            throw error;
        }
    }
    
    async updateSubcategory(id, subcategory) {
        try {
            return await this.request(`/subcategories/${id}`, {
                method: 'PUT',
                body: JSON.stringify(subcategory)
            });
        } catch (error) {
            console.error('Failed to update subcategory:', error);
            throw error;
        }
    }
    
    async deleteSubcategory(id) {
        try {
            return await this.request(`/subcategories/${id}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Failed to delete subcategory:', error);
            throw error;
        }
    }
    
    // Expenses
    async getExpenses(filters = {}) {
        try {
            const queryParams = new URLSearchParams(filters).toString();
            const url = queryParams ? `/expenses?${queryParams}` : '/expenses';
            return await this.request(url);
        } catch (error) {
            console.error('Failed to get expenses:', error);
            throw error;
        }
    }
    
    async getExpense(id) {
        try {
            return await this.request(`/expenses/${id}`);
        } catch (error) {
            console.error('Failed to get expense by ID:', error);
            throw error;
        }
    }
    
    async createExpense(expense) {
        try {
            return await this.request('/expenses', {
                method: 'POST',
                body: JSON.stringify(expense)
            });
        } catch (error) {
            console.error('Failed to create expense:', error);
            throw error;
        }
    }
    
    async updateExpense(id, expense) {
        try {
            return await this.request(`/expenses/${id}`, {
                method: 'PUT',
                body: JSON.stringify(expense)
            });
        } catch (error) {
            console.error('Failed to update expense:', error);
            throw error;
        }
    }
    
    async deleteExpense(id) {
        try {
            return await this.request(`/expenses/${id}`, {
                method: 'DELETE'
            });
        } catch (error) {
            console.error('Failed to delete expense:', error);
            throw error;
        }
    }
    
    // Summary
    async getExpenseSummary() {
        try {
            return await this.request('/expenses/summary');
        } catch (error) {
            console.error('Failed to get expense summary:', error);
            throw error;
        }
    }
}

// Initialize API service
const api = new ApiService();

// Modal utility functions
function closeModalOnBackdropClick(modalElement) {
    if (modalElement) {
        modalElement.addEventListener('click', (e) => {
            // Close modal if clicking on the backdrop (not the modal content)
            if (e.target === modalElement) {
                modalElement.classList.add('hidden');
            }
        });
    }
}

// Close modal on Escape key
function closeModalOnEscape(modalElement) {
    if (modalElement) {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && !modalElement.classList.contains('hidden')) {
                modalElement.classList.add('hidden');
            }
        });
    }
}

// Initialize modal behaviors for all modals
function initializeModals() {
    const modals = document.querySelectorAll('[id$="Modal"]');
    modals.forEach(modal => {
        closeModalOnBackdropClick(modal);
        closeModalOnEscape(modal);
    });
}

// Initialize modals when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    initializeModals();
});

// Export functions for use in other modules
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.showNotification = showNotification;
window.api = api;
window.closeModalOnBackdropClick = closeModalOnBackdropClick;
window.closeModalOnEscape = closeModalOnEscape;

console.log('Expenses Manager application loaded');