const db = require('../config/db');

class Expense {
  // Create a new expense
  static create(subcategory_id, amount, description, date, callback) {
    const sql = `INSERT INTO expenses (subcategory_id, amount, description, date) VALUES (?, ?, ?, ?)`;
    db.run(sql, [subcategory_id, amount, description, date], function(err) {
      if (err) {
        callback(err, null);
      } else {
        callback(null, { 
          id: this.lastID, 
          subcategory_id, 
          amount, 
          description, 
          date 
        });
      }
    });
  }

  // Find all expenses with optional filters
  static findAll(filters = {}, callback) {
    let sql = `
      SELECT e.*, s.name as subcategory_name, c.name as category_name
      FROM expenses e
      JOIN subcategories s ON e.subcategory_id = s.id
      JOIN categories c ON s.category_id = c.id
    `;
    
    const params = [];
    const whereConditions = [];
    
    // Add filters if provided
    if (filters.category_id) {
      whereConditions.push('c.id = ?');
      params.push(filters.category_id);
    }
    
    if (filters.subcategory_id) {
      whereConditions.push('s.id = ?');
      params.push(filters.subcategory_id);
    }
    
    if (filters.start_date) {
      whereConditions.push('e.date >= ?');
      params.push(filters.start_date);
    }
    
    if (filters.end_date) {
      whereConditions.push('e.date <= ?');
      params.push(filters.end_date);
    }
    
    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }
    
    sql += ' ORDER BY e.date DESC, e.created_at DESC';
    
    // Handle limit for dashboard recent expenses
    if (filters.limit) {
      sql += ' LIMIT ?';
      params.push(filters.limit);
    }
    
    db.all(sql, params, (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Find an expense by ID
  static findById(id, callback) {
    const sql = `
      SELECT e.*, s.name as subcategory_name, c.name as category_name
      FROM expenses e
      JOIN subcategories s ON e.subcategory_id = s.id
      JOIN categories c ON s.category_id = c.id
      WHERE e.id = ?
    `;
    db.get(sql, [id], (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, row);
      }
    });
  }

  // Update an expense
  static update(id, subcategory_id, amount, description, date, callback) {
    const sql = `UPDATE expenses SET subcategory_id = ?, amount = ?, description = ?, date = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    db.run(sql, [subcategory_id, amount, description, date, id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Expense not found'), null);
      } else {
        Expense.findById(id, callback);
      }
    });
  }

  // Delete an expense
  static delete(id, callback) {
    const sql = `DELETE FROM expenses WHERE id = ?`;
    db.run(sql, [id], function(err) {
      if (err) {
        callback(err, null);
      } else if (this.changes === 0) {
        callback(new Error('Expense not found'), null);
      } else {
        callback(null, { message: 'Expense deleted successfully' });
      }
    });
  }

  // Get expense summary by category with optional date filters
  static getSummaryByCategory(filters = {}, callback) {
    let sql = `
      SELECT
        c.name as category_name,
        COUNT(e.id) as expense_count,
        SUM(e.amount) as total_amount
      FROM expenses e
      JOIN subcategories s ON e.subcategory_id = s.id
      JOIN categories c ON s.category_id = c.id
    `;

    const params = [];
    const whereConditions = [];

    // Add date filters if provided
    if (filters.start_date) {
      whereConditions.push('e.date >= ?');
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      whereConditions.push('e.date <= ?');
      params.push(filters.end_date);
    }

    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }

    sql += `
      GROUP BY c.id, c.name
      ORDER BY total_amount DESC
    `;

    db.all(sql, params, (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Get expenses grouped by time period (daily, weekly, monthly)
  static getExpensesByTimePeriod(period = 'monthly', filters = {}, callback) {
    let dateFormat;
    let dateLabel;

    switch(period) {
      case 'daily':
        dateFormat = '%Y-%m-%d';
        dateLabel = 'date';
        break;
      case 'weekly':
        dateFormat = '%Y-%W';
        dateLabel = 'week';
        break;
      case 'monthly':
      default:
        dateFormat = '%Y-%m';
        dateLabel = 'month';
        break;
    }

    let sql = `
      SELECT
        strftime('${dateFormat}', e.date) as period,
        COUNT(e.id) as expense_count,
        SUM(e.amount) as total_amount
      FROM expenses e
    `;

    const params = [];
    const whereConditions = [];

    // Add date filters if provided
    if (filters.start_date) {
      whereConditions.push('e.date >= ?');
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      whereConditions.push('e.date <= ?');
      params.push(filters.end_date);
    }

    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }

    sql += `
      GROUP BY strftime('${dateFormat}', e.date)
      ORDER BY period ASC
    `;

    db.all(sql, params, (err, rows) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, rows);
      }
    });
  }

  // Get quick stats for a specific period
  static getQuickStats(filters = {}, callback) {
    let sql = `
      SELECT
        COUNT(e.id) as total_expenses,
        SUM(e.amount) as total_amount,
        AVG(e.amount) as average_amount,
        MIN(e.amount) as min_amount,
        MAX(e.amount) as max_amount
      FROM expenses e
    `;

    const params = [];
    const whereConditions = [];

    // Add date filters if provided
    if (filters.start_date) {
      whereConditions.push('e.date >= ?');
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      whereConditions.push('e.date <= ?');
      params.push(filters.end_date);
    }

    if (whereConditions.length > 0) {
      sql += ' WHERE ' + whereConditions.join(' AND ');
    }

    db.get(sql, params, (err, row) => {
      if (err) {
        callback(err, null);
      } else {
        callback(null, row);
      }
    });
  }

  // Helper method to get date ranges for common periods
  static getDateRangeForPeriod(period) {
    const now = new Date();

    // Helper function to format date as YYYY-MM-DD in local timezone
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch(period) {
      case 'today':
        return {
          start_date: formatDate(today),
          end_date: formatDate(today)
        };

      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return {
          start_date: formatDate(yesterday),
          end_date: formatDate(yesterday)
        };

      case 'this_week':
        const startOfWeek = new Date(today);
        // Start from Monday (1) instead of Sunday (0)
        const dayOfWeek = today.getDay();
        const daysToMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
        startOfWeek.setDate(today.getDate() - daysToMonday);
        return {
          start_date: formatDate(startOfWeek),
          end_date: formatDate(today)
        };

      case 'last_week':
        const lastWeekEnd = new Date(today);
        const dayOfWeekEnd = today.getDay();
        const daysToLastSunday = dayOfWeekEnd === 0 ? 7 : dayOfWeek;
        lastWeekEnd.setDate(today.getDate() - daysToLastSunday);
        const lastWeekStart = new Date(lastWeekEnd);
        lastWeekStart.setDate(lastWeekEnd.getDate() - 6);
        return {
          start_date: formatDate(lastWeekStart),
          end_date: formatDate(lastWeekEnd)
        };

      case 'this_month':
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        return {
          start_date: formatDate(startOfMonth),
          end_date: formatDate(today)
        };

      case 'last_month':
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        return {
          start_date: formatDate(lastMonthStart),
          end_date: formatDate(lastMonthEnd)
        };

      case 'this_quarter':
        const currentQuarter = Math.floor(now.getMonth() / 3);
        const startOfQuarter = new Date(now.getFullYear(), currentQuarter * 3, 1);
        return {
          start_date: formatDate(startOfQuarter),
          end_date: formatDate(today)
        };

      case 'this_year':
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        return {
          start_date: formatDate(startOfYear),
          end_date: formatDate(today)
        };

      default:
        return {};
    }
  }
}

module.exports = Expense;