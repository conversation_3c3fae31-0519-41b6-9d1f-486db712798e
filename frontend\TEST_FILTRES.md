# Test des Filtres - Guide de Vérification

## 🧪 Tests à Effectuer

### **1. Test du Filtre "Aujourd'hui"**

#### Dashboard (index.html)
1. Ouvrir la page Dashboard
2. Vérifier que le bouton "Aujourd'hui" est actif (couleur indigo) par défaut
3. <PERSON><PERSON><PERSON> sur "Aujourd'hui" 
4. Vérifier que les données affichées correspondent aux dépenses d'aujourd'hui
5. Vérifier que les graphiques se mettent à jour

#### Reports (reports.html)
1. Ouvrir la page Reports
2. Vérifier que le bouton "Aujourd'hui" est actif par défaut
3. Cliquer sur "Aujourd'hui"
4. Vérifier que les graphiques et tableaux affichent les données d'aujourd'hui

#### Expenses (expenses.html)
1. Ouvrir la page Expenses
2. Vérifier que le bouton "Aujourd'hui" est actif par défaut
3. Vérifier que seules les dépenses d'aujourd'hui sont affichées dans le tableau

### **2. Test des Autres Filtres de Période**

Pour chaque page (Dashboard, Reports, Expenses) :

#### Cette Semaine
1. Cliquer sur "Cette semaine"
2. Vérifier que le bouton devient actif
3. Vérifier que les données affichées correspondent à la semaine en cours

#### Ce Mois
1. Cliquer sur "Ce mois"
2. Vérifier que les données du mois en cours s'affichent

#### Ce Trimestre (Dashboard/Reports uniquement)
1. Cliquer sur "Ce trimestre"
2. Vérifier les données du trimestre

#### Cette Année (Dashboard/Reports uniquement)
1. Cliquer sur "Cette année"
2. Vérifier les données de l'année

### **3. Test de la Période Personnalisée (Modale)**

#### Dashboard
1. Cliquer sur "Période personnalisée"
2. **Vérifier qu'une modale s'ouvre** (pas d'expansion dans la page)
3. Vérifier que les champs de date ont des valeurs par défaut
4. Sélectionner une date de début et une date de fin
5. Cliquer sur "Appliquer"
6. Vérifier que la modale se ferme
7. Vérifier que les données correspondent à la période sélectionnée

#### Reports
1. Répéter les mêmes tests que pour Dashboard
2. Vérifier que les graphiques se mettent à jour correctement

#### Tests de Fermeture de Modale
1. Ouvrir la modale de période personnalisée
2. Cliquer sur le bouton "X" → modale doit se fermer
3. Ouvrir la modale, cliquer sur "Annuler" → modale doit se fermer
4. Ouvrir la modale, cliquer à l'extérieur → modale doit se fermer
5. Ouvrir la modale, appuyer sur Échap → modale doit se fermer

### **4. Test de Cohérence des Données**

#### Vérification avec des Données Réelles
1. Ajouter quelques dépenses avec des dates différentes :
   - Une dépense aujourd'hui
   - Une dépense cette semaine
   - Une dépense le mois dernier

2. Tester chaque filtre et vérifier que :
   - "Aujourd'hui" affiche seulement la dépense d'aujourd'hui
   - "Cette semaine" affiche les dépenses de la semaine
   - "Ce mois" affiche les dépenses du mois
   - Les totaux sont corrects

### **5. Test des Messages et Notifications**

1. Appliquer un filtre → vérifier qu'une notification de confirmation apparaît
2. Essayer une période personnalisée invalide → vérifier le message d'erreur
3. Vérifier que les labels des périodes sont corrects en français

### **6. Test de l'Interface Responsive**

1. Tester sur différentes tailles d'écran
2. Vérifier que les boutons de filtre s'adaptent bien
3. Vérifier que la modale s'affiche correctement sur mobile

## 🔧 Problèmes Potentiels à Vérifier

### **Si "Aujourd'hui" ne fonctionne pas :**
1. Ouvrir la console du navigateur (F12)
2. Vérifier s'il y a des erreurs JavaScript
3. Vérifier les logs de debug :
   - "Loading expenses with filters: ..."
   - "Loaded X expenses"
   - "Expense filters: ..."

### **Si les Graphiques ne se Mettent pas à Jour :**
1. Vérifier la console pour des erreurs d'API
2. Vérifier que les données sont bien reçues du backend
3. Vérifier que Chart.js fonctionne correctement

### **Si la Modale ne s'Ouvre pas :**
1. Vérifier qu'il n'y a pas d'erreurs JavaScript
2. Vérifier que les IDs des éléments correspondent
3. Vérifier que les classes CSS sont bien définies

## 📊 Données de Test Recommandées

Pour tester efficacement, créer ces dépenses :

```
Dépense 1: 50€ - Alimentation - Aujourd'hui
Dépense 2: 30€ - Transport - Hier  
Dépense 3: 100€ - Loisirs - Il y a 3 jours
Dépense 4: 200€ - Logement - Il y a 1 semaine
Dépense 5: 75€ - Santé - Il y a 1 mois
```

Avec ces données :
- "Aujourd'hui" devrait afficher : 50€ (1 dépense)
- "Cette semaine" devrait afficher : 180€ (3 dépenses)
- "Ce mois" devrait afficher : 380€ (4 dépenses)

## ✅ Critères de Réussite

- [ ] Filtre "Aujourd'hui" actif par défaut sur toutes les pages
- [ ] Tous les filtres de période fonctionnent correctement
- [ ] Période personnalisée s'ouvre dans une modale
- [ ] Modale se ferme correctement (X, Annuler, clic extérieur, Échap)
- [ ] Données affichées correspondent aux filtres appliqués
- [ ] Graphiques se mettent à jour en temps réel
- [ ] Notifications de confirmation apparaissent
- [ ] Interface responsive et cohérente
- [ ] Pas d'erreurs dans la console du navigateur
