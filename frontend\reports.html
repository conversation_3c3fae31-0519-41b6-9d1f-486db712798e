<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapports - Expenses Manager</title>
    <link href="css/tailwind.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-gray-900 to-gray-800 text-white min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-wallet text-indigo-500 text-2xl mr-2"></i>
                        <span class="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">
                            Expenses Manager
                        </span>
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="/" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-home mr-1"></i> Tableau de bord
                            </a>
                            <a href="/categories" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-tags mr-1"></i> Catégories
                            </a>
                            <a href="/subcategories" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-list mr-1"></i> Sous-catégories
                            </a>
                            <a href="/expenses" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-receipt mr-1"></i> Dépenses
                            </a>
                            <a href="#" class="bg-gray-900 text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-chart-line mr-1"></i> Rapports
                            </a>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6">
                        <button class="bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none">
                            <i class="fas fa-moon"></i>
                        </button>
                        <div class="ml-3 relative">
                            <div>
                                <button class="max-w-xs flex items-center text-sm rounded-full focus:outline-none">
                                    <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Utilisateur&background=0D8ABC&color=fff" alt="">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="-mr-2 flex md:hidden">
                    <button class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold">Rapports et Analyses</h1>
                <p class="text-gray-400 mt-2">Analysez vos dépenses et identifiez des tendances</p>
            </div>
            <div class="flex space-x-3">
                <button id="exportReport" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition">
                    <i class="fas fa-download mr-2"></i> Exporter
                </button>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
            <div class="flex flex-wrap items-center justify-between gap-4 mb-4">
                <h2 class="text-lg font-semibold">Filtres de Période</h2>
                <div class="flex flex-wrap gap-2">
                    <button id="todayFilter" class="px-3 py-1 text-sm bg-indigo-600 rounded-lg transition">Aujourd'hui</button>
                    <button id="thisWeekFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Cette semaine</button>
                    <button id="thisMonthFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Ce mois</button>
                    <button id="thisQuarterFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Ce trimestre</button>
                    <button id="thisYearFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Cette année</button>
                    <button id="allTimeFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Tout le temps</button>
                    <button id="customPeriodBtn" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Période personnalisée</button>
                </div>
            </div>


        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-gradient-to-r from-indigo-600 to-indigo-800 rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-indigo-500 bg-opacity-30 mr-4">
                        <i class="fas fa-wallet text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-indigo-200">Total Dépenses</p>
                        <p id="totalExpenses" class="text-2xl font-bold">€0.00</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-purple-600 to-purple-800 rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-purple-500 bg-opacity-30 mr-4">
                        <i class="fas fa-tags text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-purple-200">Catégories</p>
                        <p id="categoryCount" class="text-2xl font-bold">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-cyan-600 to-cyan-800 rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-cyan-500 bg-opacity-30 mr-4">
                        <i class="fas fa-receipt text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-cyan-200">Transactions</p>
                        <p id="transactionCount" class="text-2xl font-bold">0</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-gradient-to-r from-green-600 to-green-800 rounded-xl shadow-lg p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-lg bg-green-500 bg-opacity-30 mr-4">
                        <i class="fas fa-chart-line text-2xl"></i>
                    </div>
                    <div>
                        <p class="text-sm text-green-200">Moyenne Mensuelle</p>
                        <p id="averageMonthly" class="text-2xl font-bold">€0.00</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Expenses by Category Chart -->
            <div class="bg-gray-800 rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">Dépenses par Catégorie</h2>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-gray-700 rounded-lg">Mois</button>
                        <button class="px-3 py-1 text-sm bg-indigo-600 rounded-lg">Trimestre</button>
                        <button class="px-3 py-1 text-sm bg-gray-700 rounded-lg">Année</button>
                    </div>
                </div>
                <div class="h-80 flex items-center justify-center bg-gray-900 rounded-lg">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
            
            <!-- Expenses Over Time Chart -->
            <div class="bg-gray-800 rounded-xl shadow-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-bold">Évolution des Dépenses</h2>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm bg-gray-700 rounded-lg">Mois</button>
                        <button class="px-3 py-1 text-sm bg-indigo-600 rounded-lg">Trimestre</button>
                        <button class="px-3 py-1 text-sm bg-gray-700 rounded-lg">Année</button>
                    </div>
                </div>
                <div class="h-80 flex items-center justify-center bg-gray-900 rounded-lg">
                    <canvas id="timeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Categories Table -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-bold mb-6">Catégories Principales</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-700">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Catégorie</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Nombre de Transactions</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Montant Total</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Pourcentage</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700" id="categoriesTableBody">
                        <!-- Categories will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
        </div>
    </main>

    <!-- Custom Period Modal -->
    <div id="customPeriodModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-gray-800 rounded-xl shadow-xl max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-semibold">Période Personnalisée</h3>
                    <button id="closeCustomPeriodModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-2">Date de début</label>
                        <input type="date" id="customStartDate" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-400 mb-2">Date de fin</label>
                        <input type="date" id="customEndDate" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button id="cancelCustomPeriod" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition">
                        Annuler
                    </button>
                    <button id="applyCustomPeriod" class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg transition">
                        Appliquer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/main.js"></script>
    <script src="js/date-utils.js"></script>
    <script src="js/reports.js"></script>
</body>
</html>