const Expense = require('../models/Expense');

// Create a new expense
exports.createExpense = (req, res) => {
  const { subcategory_id, amount, description, date } = req.body;
  
  if (!subcategory_id || !amount || !date) {
    return res.status(400).json({ error: 'Subcategory ID, amount, and date are required' });
  }
  
  // Validate amount is a number
  if (isNaN(amount) || parseFloat(amount) <= 0) {
    return res.status(400).json({ error: 'Amount must be a positive number' });
  }
  
  Expense.create(subcategory_id, parseFloat(amount), description, date, (err, expense) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to create expense' });
    }
    res.status(201).json(expense);
  });
};

// Get all expenses with optional filters
exports.getAllExpenses = (req, res) => {
  const filters = {};

  // Handle period filter first
  if (req.query.period) {
    const dateRange = Expense.getDateRangeForPeriod(req.query.period);
    Object.assign(filters, dateRange);
  }

  if (req.query.category_id) {
    filters.category_id = req.query.category_id;
  }

  if (req.query.subcategory_id) {
    filters.subcategory_id = req.query.subcategory_id;
  }

  // Custom date range overrides period filter
  if (req.query.start_date) {
    filters.start_date = req.query.start_date;
  }

  if (req.query.end_date) {
    filters.end_date = req.query.end_date;
  }

  // Handle limit for dashboard recent expenses
  if (req.query.limit) {
    filters.limit = parseInt(req.query.limit);
  }

  console.log('Expense filters:', filters); // Debug log

  Expense.findAll(filters, (err, expenses) => {
    if (err) {
      console.error('Error retrieving expenses:', err);
      return res.status(500).json({ error: 'Failed to retrieve expenses' });
    }
    console.log(`Found ${expenses.length} expenses`); // Debug log
    res.json(expenses);
  });
};

// Get an expense by ID
exports.getExpenseById = (req, res) => {
  const { id } = req.params;
  
  Expense.findById(id, (err, expense) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to retrieve expense' });
    }
    if (!expense) {
      return res.status(404).json({ error: 'Expense not found' });
    }
    res.json(expense);
  });
};

// Update an expense
exports.updateExpense = (req, res) => {
  const { id } = req.params;
  const { subcategory_id, amount, description, date } = req.body;
  
  if (!subcategory_id || !amount || !date) {
    return res.status(400).json({ error: 'Subcategory ID, amount, and date are required' });
  }
  
  // Validate amount is a number
  if (isNaN(amount) || parseFloat(amount) <= 0) {
    return res.status(400).json({ error: 'Amount must be a positive number' });
  }
  
  Expense.update(id, subcategory_id, parseFloat(amount), description, date, (err, expense) => {
    if (err) {
      if (err.message === 'Expense not found') {
        return res.status(404).json({ error: 'Expense not found' });
      }
      return res.status(500).json({ error: 'Failed to update expense' });
    }
    res.json(expense);
  });
};

// Delete an expense
exports.deleteExpense = (req, res) => {
  const { id } = req.params;
  
  Expense.delete(id, (err, result) => {
    if (err) {
      if (err.message === 'Expense not found') {
        return res.status(404).json({ error: 'Expense not found' });
      }
      return res.status(500).json({ error: 'Failed to delete expense' });
    }
    res.json(result);
  });
};

// Get expense summary by category with optional filters
exports.getExpenseSummary = (req, res) => {
  const filters = {};

  // Handle period filter
  if (req.query.period) {
    const dateRange = Expense.getDateRangeForPeriod(req.query.period);
    Object.assign(filters, dateRange);
  }

  // Handle custom date range
  if (req.query.start_date) {
    filters.start_date = req.query.start_date;
  }

  if (req.query.end_date) {
    filters.end_date = req.query.end_date;
  }

  Expense.getSummaryByCategory(filters, (err, summary) => {
    if (err) {
      console.error('Error retrieving expense summary:', err);
      return res.status(500).json({ error: 'Failed to retrieve expense summary' });
    }
    console.log('Expense summary:', summary); // Log for debugging
    res.json(summary);
  });
};

// Get expenses by time period for charts
exports.getExpensesByTimePeriod = (req, res) => {
  const period = req.query.period || 'monthly'; // daily, weekly, monthly
  const filters = {};

  // Handle date range
  if (req.query.start_date) {
    filters.start_date = req.query.start_date;
  }

  if (req.query.end_date) {
    filters.end_date = req.query.end_date;
  }

  // Handle predefined periods
  if (req.query.time_period) {
    const dateRange = Expense.getDateRangeForPeriod(req.query.time_period);
    Object.assign(filters, dateRange);
  }

  Expense.getExpensesByTimePeriod(period, filters, (err, data) => {
    if (err) {
      console.error('Error retrieving expenses by time period:', err);
      return res.status(500).json({ error: 'Failed to retrieve expenses by time period' });
    }
    res.json(data);
  });
};

// Get quick stats for dashboard
exports.getQuickStats = (req, res) => {
  const filters = {};

  // Handle period filter
  if (req.query.period) {
    const dateRange = Expense.getDateRangeForPeriod(req.query.period);
    Object.assign(filters, dateRange);
  }

  // Handle custom date range
  if (req.query.start_date) {
    filters.start_date = req.query.start_date;
  }

  if (req.query.end_date) {
    filters.end_date = req.query.end_date;
  }

  Expense.getQuickStats(filters, (err, stats) => {
    if (err) {
      console.error('Error retrieving quick stats:', err);
      return res.status(500).json({ error: 'Failed to retrieve quick stats' });
    }
    res.json(stats);
  });
};