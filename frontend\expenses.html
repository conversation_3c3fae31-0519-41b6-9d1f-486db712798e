<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>D<PERSON><PERSON><PERSON> - Expenses Manager</title>
    <link href="css/tailwind.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gradient-to-br from-gray-900 to-gray-800 text-white min-h-screen">
    <!-- Navigation -->
    <nav class="bg-gray-800 border-b border-gray-700">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-wallet text-indigo-500 text-2xl mr-2"></i>
                        <span class="text-xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-500">
                            Expenses Manager
                        </span>
                    </div>
                    <div class="hidden md:block">
                        <div class="ml-10 flex items-baseline space-x-4">
                            <a href="/" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-home mr-1"></i> Tableau de bord
                            </a>
                            <a href="/categories" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-tags mr-1"></i> Catégories
                            </a>
                            <a href="/subcategories" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-list mr-1"></i> Sous-catégories
                            </a>
                            <a href="#" class="bg-gray-900 text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-receipt mr-1"></i> Dépenses
                            </a>
                            <a href="/reports" class="text-gray-300 hover:bg-gray-700 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                                <i class="fas fa-chart-line mr-1"></i> Rapports
                            </a>
                        </div>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-4 flex items-center md:ml-6">
                        <button class="bg-gray-800 p-1 rounded-full text-gray-400 hover:text-white focus:outline-none">
                            <i class="fas fa-moon"></i>
                        </button>
                        <div class="ml-3 relative">
                            <div>
                                <button class="max-w-xs flex items-center text-sm rounded-full focus:outline-none">
                                    <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name=Utilisateur&background=0D8ABC&color=fff" alt="">
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="-mr-2 flex md:hidden">
                    <button class="bg-gray-800 inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 focus:outline-none">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold">Gestion des Dépenses</h1>
                <p class="text-gray-400 mt-2">Enregistrez et suivez vos dépenses</p>
            </div>
            <button id="addExpenseBtn" class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg transition flex items-center">
                <i class="fas fa-plus mr-2"></i> Nouvelle Dépense
            </button>
        </div>

        <!-- Filters -->
        <div class="bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
            <!-- Quick Period Filters -->
            <div class="flex flex-wrap items-center justify-between gap-4 mb-4">
                <h2 class="text-lg font-semibold">Filtres Rapides</h2>
                <div class="flex flex-wrap gap-2">
                    <button id="todayFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Aujourd'hui</button>
                    <button id="thisWeekFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Cette semaine</button>
                    <button id="thisMonthFilter" class="px-3 py-1 text-sm bg-gray-700 hover:bg-indigo-600 rounded-lg transition">Ce mois</button>
                    <button id="allExpensesFilter" class="px-3 py-1 text-sm bg-indigo-600 rounded-lg">Toutes</button>
                </div>
            </div>

            <!-- Detailed Filters -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-400 mb-2">Catégorie</label>
                    <select id="filterCategory" class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <option value="">Toutes les catégories</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-400 mb-2">Date de début</label>
                    <input
                        type="date"
                        id="filterStartDate"
                        class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    >
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-400 mb-2">Date de fin</label>
                    <input
                        type="date"
                        id="filterEndDate"
                        class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                    >
                </div>
                <div class="flex items-end">
                    <button id="filterButton" class="w-full px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition">
                        <i class="fas fa-filter mr-2"></i> Filtrer
                    </button>
                </div>
            </div>
        </div>

        <!-- Expenses Table -->
        <div class="bg-gray-800 rounded-xl shadow-lg overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-700 table">
                    <thead class="bg-gray-750">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Description</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Catégorie</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Sous-catégorie</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Montant</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700" id="expensesTableBody">
                        <!-- Expenses will be dynamically inserted here -->
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="bg-gray-750 px-6 py-3 flex items-center justify-between border-t border-gray-700">
                <div class="text-sm text-gray-400">
                    Affichage de <span class="font-medium">0</span> à <span class="font-medium">0</span> sur <span class="font-medium">0</span> résultats
                </div>
                <div class="flex space-x-2">
                    <button class="px-3 py-1 rounded-lg bg-gray-700 text-gray-400 cursor-not-allowed">
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="px-3 py-1 rounded-lg bg-indigo-600 text-white">
                        1
                    </button>
                    <button class="px-3 py-1 rounded-lg bg-gray-700 text-gray-400 cursor-not-allowed">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Expense Modal -->
    <div id="expenseModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-800 rounded-xl shadow-2xl w-full max-w-md mx-4">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold" id="modalTitle">Nouvelle Dépense</h3>
                    <button id="closeModal" class="text-gray-400 hover:text-white">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="expenseForm">
                    <input type="hidden" id="expenseId">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-400 mb-2" for="expenseDescription">
                            Description
                        </label>
                        <input 
                            type="text" 
                            id="expenseDescription" 
                            class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            placeholder="Ex: Courses, Essence..."
                        >
                    </div>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2" for="expenseCategory">
                                Catégorie
                            </label>
                            <select 
                                id="expenseCategory" 
                                class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            >
                                <option value="">Sélectionner une catégorie</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2" for="expenseSubcategory">
                                Sous-catégorie
                            </label>
                            <select 
                                id="expenseSubcategory" 
                                class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            >
                                <option value="">Sélectionner une sous-catégorie</option>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2" for="expenseAmount">
                                Montant (€)
                            </label>
                            <input 
                                type="number" 
                                id="expenseAmount" 
                                step="0.01"
                                min="0"
                                class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                placeholder="0.00"
                            >
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-400 mb-2" for="expenseDate">
                                Date
                            </label>
                            <input 
                                type="date" 
                                id="expenseDate" 
                                class="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                            >
                        </div>
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button 
                            type="button" 
                            id="cancelModal" 
                            class="px-4 py-2 border border-gray-600 rounded-lg hover:bg-gray-700 transition"
                        >
                            Annuler
                        </button>
                        <button 
                            type="submit" 
                            class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg transition"
                        >
                            Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="js/main.js"></script>
    <script src="js/date-utils.js"></script>
    <script src="js/expenses.js"></script>
</body>
</html>